package view;

import model.DichVu;
import model.DichVuDAO;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.math.BigDecimal;
import java.util.List;

/**
 * Quản lý dịch vụ - Phiên bản đơn giản không dùng Form Editor
 * <AUTHOR>
 */
public class QuanLyDichVuSimple extends JPanel {
    
    private DichVuDAO dichVuDAO;
    private DefaultTableModel tableModel;
    private int selectedRow = -1;
    
    // Components
    private JTextField txttendichvu;
    private JTextField txtgia;
    private JTextField txtmota;
    private JButton btnthem;
    private JButton btnsua;
    private JButton btnxoa;
    private JTable tblchitietdichvu;

    public QuanLyDichVuSimple() {
        try {
            dichVuDAO = new DichVuDAO();
            initComponents();
            setupTable();
            setupEventHandlers();
            loadData();
        } catch (Exception e) {
            System.err.println("❌ Lỗi khởi tạo QuanLyDichVu: " + e.getMessage());
            e.printStackTrace();
            JOptionPane.showMessageDialog(this, 
                "Lỗi khởi tạo quản lý dịch vụ. Kiểm tra kết nối database!", 
                "Lỗi", 
                JOptionPane.ERROR_MESSAGE);
        }
    }
    
    private void initComponents() {
        setLayout(new BorderLayout());
        
        // Panel tiêu đề
        JPanel titlePanel = new JPanel();
        JLabel titleLabel = new JLabel("QUẢN LÝ DỊCH VỤ");
        titleLabel.setFont(new Font("Arial", Font.BOLD, 18));
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        titlePanel.add(titleLabel);
        add(titlePanel, BorderLayout.NORTH);
        
        // Panel form nhập liệu
        JPanel formPanel = new JPanel(new GridBagLayout());
        formPanel.setBorder(BorderFactory.createTitledBorder("Thông tin dịch vụ"));
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        
        // Tên dịch vụ
        gbc.gridx = 0; gbc.gridy = 0;
        formPanel.add(new JLabel("Tên dịch vụ:"), gbc);
        gbc.gridx = 1;
        txttendichvu = new JTextField(20);
        formPanel.add(txttendichvu, gbc);
        
        // Giá
        gbc.gridx = 0; gbc.gridy = 1;
        formPanel.add(new JLabel("Giá (VNĐ):"), gbc);
        gbc.gridx = 1;
        txtgia = new JTextField(20);
        formPanel.add(txtgia, gbc);
        
        // Mô tả
        gbc.gridx = 0; gbc.gridy = 2;
        formPanel.add(new JLabel("Mô tả:"), gbc);
        gbc.gridx = 1;
        txtmota = new JTextField(20);
        formPanel.add(txtmota, gbc);
        
        // Buttons
        JPanel buttonPanel = new JPanel(new FlowLayout());
        btnthem = new JButton("Thêm");
        btnsua = new JButton("Sửa");
        btnxoa = new JButton("Xóa");
        
        buttonPanel.add(btnthem);
        buttonPanel.add(btnsua);
        buttonPanel.add(btnxoa);
        
        gbc.gridx = 0; gbc.gridy = 3;
        gbc.gridwidth = 2;
        formPanel.add(buttonPanel, gbc);
        
        add(formPanel, BorderLayout.WEST);
        
        // Table
        String[] columnNames = {"Mã DV", "Tên dịch vụ", "Giá", "Mô tả"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        tblchitietdichvu = new JTable(tableModel);
        JScrollPane scrollPane = new JScrollPane(tblchitietdichvu);
        scrollPane.setBorder(BorderFactory.createTitledBorder("Danh sách dịch vụ"));
        add(scrollPane, BorderLayout.CENTER);
    }
    
    private void setupTable() {
        // Thiết lập độ rộng cột
        tblchitietdichvu.getColumnModel().getColumn(0).setPreferredWidth(60);
        tblchitietdichvu.getColumnModel().getColumn(1).setPreferredWidth(200);
        tblchitietdichvu.getColumnModel().getColumn(2).setPreferredWidth(100);
        tblchitietdichvu.getColumnModel().getColumn(3).setPreferredWidth(300);
        
        // Xử lý sự kiện chọn dòng
        tblchitietdichvu.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                selectedRow = tblchitietdichvu.getSelectedRow();
                if (selectedRow >= 0) {
                    loadSelectedRowToForm();
                }
            }
        });
    }
    
    private void setupEventHandlers() {
        btnthem.addActionListener(e -> themDichVu());
        btnsua.addActionListener(e -> suaDichVu());
        btnxoa.addActionListener(e -> xoaDichVu());
    }
    
    private void loadData() {
        tableModel.setRowCount(0);
        List<DichVu> danhSach = dichVuDAO.layTatCaDichVu();
        
        for (DichVu dv : danhSach) {
            Object[] row = {
                dv.getMaDV(),
                dv.getTenDV(),
                String.format("%,.0f VNĐ", dv.getGia().doubleValue()),
                dv.getMoTa()
            };
            tableModel.addRow(row);
        }
    }
    
    private void loadSelectedRowToForm() {
        if (selectedRow >= 0) {
            txttendichvu.setText(tableModel.getValueAt(selectedRow, 1).toString());
            String giaStr = tableModel.getValueAt(selectedRow, 2).toString();
            // Loại bỏ " VNĐ" và dấu phẩy
            giaStr = giaStr.replace(" VNĐ", "").replace(",", "");
            txtgia.setText(giaStr);
            txtmota.setText(tableModel.getValueAt(selectedRow, 3).toString());
        }
    }
    
    private void clearForm() {
        txttendichvu.setText("");
        txtgia.setText("");
        txtmota.setText("");
        selectedRow = -1;
        tblchitietdichvu.clearSelection();
    }
    
    private void themDichVu() {
        if (!validateForm()) {
            return;
        }
        
        String tenDV = txttendichvu.getText().trim();
        BigDecimal gia = new BigDecimal(txtgia.getText().trim());
        String moTa = txtmota.getText().trim();
        
        DichVu dv = new DichVu(tenDV, gia, moTa);
        
        if (dichVuDAO.themDichVu(dv)) {
            JOptionPane.showMessageDialog(this, "Thêm dịch vụ thành công!", "Thông báo", JOptionPane.INFORMATION_MESSAGE);
            loadData();
            clearForm();
        } else {
            JOptionPane.showMessageDialog(this, "Thêm dịch vụ thất bại!", "Lỗi", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    private void suaDichVu() {
        if (selectedRow < 0) {
            JOptionPane.showMessageDialog(this, "Vui lòng chọn dịch vụ cần sửa!", "Thông báo", JOptionPane.WARNING_MESSAGE);
            return;
        }
        
        if (!validateForm()) {
            return;
        }
        
        int maDV = (Integer) tableModel.getValueAt(selectedRow, 0);
        String tenDV = txttendichvu.getText().trim();
        BigDecimal gia = new BigDecimal(txtgia.getText().trim());
        String moTa = txtmota.getText().trim();
        
        DichVu dv = new DichVu();
        dv.setMaDV(maDV);
        dv.setTenDV(tenDV);
        dv.setGia(gia);
        dv.setMoTa(moTa);
        
        if (dichVuDAO.suaDichVu(dv)) {
            JOptionPane.showMessageDialog(this, "Sửa thông tin dịch vụ thành công!", "Thông báo", JOptionPane.INFORMATION_MESSAGE);
            loadData();
            clearForm();
        } else {
            JOptionPane.showMessageDialog(this, "Sửa thông tin dịch vụ thất bại!", "Lỗi", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    private void xoaDichVu() {
        if (selectedRow < 0) {
            JOptionPane.showMessageDialog(this, "Vui lòng chọn dịch vụ cần xóa!", "Thông báo", JOptionPane.WARNING_MESSAGE);
            return;
        }
        
        int maDV = (Integer) tableModel.getValueAt(selectedRow, 0);
        String tenDV = tableModel.getValueAt(selectedRow, 1).toString();
        
        int confirm = JOptionPane.showConfirmDialog(this, 
            "Bạn có chắc chắn muốn xóa dịch vụ: " + tenDV + "?", 
            "Xác nhận xóa", 
            JOptionPane.YES_NO_OPTION);
        
        if (confirm == JOptionPane.YES_OPTION) {
            if (dichVuDAO.xoaDichVu(maDV)) {
                JOptionPane.showMessageDialog(this, "Xóa dịch vụ thành công!", "Thông báo", JOptionPane.INFORMATION_MESSAGE);
                loadData();
                clearForm();
            } else {
                JOptionPane.showMessageDialog(this, "Xóa dịch vụ thất bại! Có thể dịch vụ đã được sử dụng.", "Lỗi", JOptionPane.ERROR_MESSAGE);
            }
        }
    }
    
    private boolean validateForm() {
        String tenDV = txttendichvu.getText().trim();
        String giaStr = txtgia.getText().trim();
        String moTa = txtmota.getText().trim();
        
        if (tenDV.isEmpty()) {
            JOptionPane.showMessageDialog(this, "Vui lòng nhập tên dịch vụ!", "Lỗi", JOptionPane.ERROR_MESSAGE);
            txttendichvu.requestFocus();
            return false;
        }
        
        if (giaStr.isEmpty()) {
            JOptionPane.showMessageDialog(this, "Vui lòng nhập giá dịch vụ!", "Lỗi", JOptionPane.ERROR_MESSAGE);
            txtgia.requestFocus();
            return false;
        }
        
        try {
            BigDecimal gia = new BigDecimal(giaStr);
            if (gia.compareTo(BigDecimal.ZERO) <= 0) {
                JOptionPane.showMessageDialog(this, "Giá dịch vụ phải lớn hơn 0!", "Lỗi", JOptionPane.ERROR_MESSAGE);
                txtgia.requestFocus();
                return false;
            }
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(this, "Giá dịch vụ không hợp lệ!", "Lỗi", JOptionPane.ERROR_MESSAGE);
            txtgia.requestFocus();
            return false;
        }
        
        if (moTa.isEmpty()) {
            JOptionPane.showMessageDialog(this, "Vui lòng nhập mô tả dịch vụ!", "Lỗi", JOptionPane.ERROR_MESSAGE);
            txtmota.requestFocus();
            return false;
        }
        
        return true;
    }
}
