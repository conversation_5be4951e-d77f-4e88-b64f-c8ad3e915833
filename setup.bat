@echo off
echo ========================================
echo    Thiet lap Ung dung Quan ly Tiem Giat Ui
echo ========================================
echo.

REM Tao thu muc lib neu chua co
if not exist "lib" mkdir "lib"

REM Tao thu muc target neu chua co
if not exist "target" mkdir "target"
if not exist "target\classes" mkdir "target\classes"
if not exist "target\dependency" mkdir "target\dependency"

echo [INFO] Dang download SQL Server JDBC Driver...

REM Download SQL Server JDBC Driver
powershell -Command "& {Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/com/microsoft/sqlserver/mssql-jdbc/12.6.1.jre11/mssql-jdbc-12.6.1.jre11.jar' -OutFile 'lib\mssql-jdbc-12.6.1.jre11.jar'}"

if exist "lib\mssql-jdbc-12.6.1.jre11.jar" (
    echo [SUCCESS] Da download thanh cong SQL Server JDBC Driver
    
    REM Copy vao target/dependency
    copy "lib\mssql-jdbc-12.6.1.jre11.jar" "target\dependency\"
    
) else (
    echo [ERROR] Khong the download JDBC Driver
    echo Vui long download thu cong tu:
    echo https://repo1.maven.org/maven2/com/microsoft/sqlserver/mssql-jdbc/12.6.1.jre11/mssql-jdbc-12.6.1.jre11.jar
    echo Va dat vao thu muc lib/
)

echo.
echo [INFO] Thiet lap hoan tat!
echo [INFO] Ban co the chay ung dung bang cach:
echo   1. Double-click vao run.bat
echo   2. Hoac chay lenh: java -cp "target/classes;lib/*" com.mycompany.giatui.app.GiatUiAPP
echo.
pause
